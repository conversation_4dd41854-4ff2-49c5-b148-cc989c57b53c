translation:
  lang_emoji: 🇺🇸
  app_title: Pizza-Byte
  loading_menu: Loading menu...

  errors:
    failed_to_load_menu: 'Menu loading error: {message}'

  header:
    adminlinks:
      prepare: Preparation
      manage_db: Database
      manage_orders: Orders
      manage_items: Items
      logout: Logout
    menu:
      login: Login
      home: Home
      orders: Orders

  footer:
    fuel: Code & Pizza by

  order:
    # When ordering something
    introduction:
      choose_your_item: Choose product
      pick_up_time: Pickup time
      pay_in_cash: Cash payment
      closer: Have a great evening!
    item:
      button:
        add: Add
    order_summary:
      add: Add pizzas
      open_order_summary: Go to cart
    order_button:
      order_now: Order

  order_status:
    order: Order
    total: Total

    suspense:
      loading: Loading...
    show_at_pickup: Show this page at pickup
    thank_you:
      qr:
        title: Thanks for your order! 🍕
        your_order_number: 'Order No.:'
        instructions: Please pay at the counter.
        order_again: New order
        view_orders: My orders
    status:
      ready_by: Ready by
      ordered: Ordered
      inPreparation: In preparation
      ready: Ready for pickup!
      delivered: Picked up
      cancelled: Cancelled
      default: Status unknown :(
      paid: Paid
      unpaid: Unpaid
      error: Error
    cancel_order: Cancel order

  cart:
    title: Cart
    messages:
      empty_cart: Cart empty.
      add_note: Add note
      add_more_products: Add more items
      name_not_set: Name missing
      timeslot_not_selected: Time slot missing
    timeslot:
      title: Time slot
      subtitle: Choose pickup time.
    order:
      name: Name
      name_placeholder: Your name
    items: Items
    total_price: Total
    currency: €
    select_timeslot: Select your desired pickup time
    open_order_summary: Go to overview

  order_overview:
    history:
      title: History
      subtitle: Your recent orders.
    recent:
      title: Current
      orders_found: '{count, plural, =0 {No orders} =1 {One order} other {# orders}} '
      no_orders_yet: No orders yet.
      messages:
        first_order: You'll see your orders here.
    timeline:
      title: Timeline
      subtitle: Live orders.

  admin:
    manage:
      database:
        title: Database
        enable_changes: Enable changes
        reset_database: Reset DB
        delete_database: Delete DB
        system_state: 'System: {state}'

      order:
        errors:
          order_not_found: Order not found.
          barcode_fail: 'Barcode error: ID not readable'
        order_history: History
        scan_qr_code_text: QR scan for order
        show_finished: Show completed
        hide_finished: Hide completed
        status: 'Status:'
        price: 'Price:'
        items: 'Items:'
        order_date: 'Date:'
        timeslot: 'Time slot:'
        comment: 'Note:'
        paid: Paid
        not_paid: Outstanding

      pizza:
        errors:
          error_fetching: 'Error: Items not loaded'
        title: Items
        subtitle: Create & edit items.
        new_pizza_name: New item
        edit_item:
          enabled: Active
          disabled: Inactive
          disable: Disable
          enable: Enable
          items_available: '{items} available'
          update: Save
          create: Create
          name: Name
          type: Type
          dietary: Details
          price: Price
          max_items_available: Max. available

    prepare:
      title: Active Orders
      subtitle: Tap when pizza is ready for oven
      needed: '{amount} needed'
      current_time: 'Time: {currentTime}'
      open_orders: 'Open: '
      no_open_orders: No open orders 🎉
      item: Item
      timeslot: Time slot
      name: Name
      status: Status
      is_done: ✅ Done

  withsystemcheck:
    check_system_status: Checking system...
    system_inactive: System inactive
    system_unavailable_message: System maintenance. Please try again later.

  components:
    menusection:
      loading: Loading menu...
    timeline:
      loading: Loading timeline...
      warning: 'Warning: {error}. Showing fallback data.'
    errormessage:
      please_try_again: Try again.
    searchinput:
      placeholder: Search...

  login:
    title: Helper Login # Or: Login
    fsik_token: FSI/K Token
    authenticating: Authenticating...
    login: Login
