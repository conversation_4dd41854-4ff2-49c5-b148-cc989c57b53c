/* General Styles */
@import "tailwindcss";

@theme {
    --color-primary-50: #f1f4ff;
    --color-primary-100: #e5e8ff;
    --color-primary-200: #ced5ff;
    --color-primary-300: #a7b1ff;
    --color-primary-400: #767fff;
    --color-primary-500: #3f42ff;
    --color-primary-600: #2118ff;
    --color-primary-700: #1007fa;
    --color-primary-800: #0d05d2;
    --color-primary-900: #0c06ac;
    --color-primary-950: #000080;
}

:root {
    --text-color: #202020;
    --box-color: #ffffff;
    --box-hover-color: #dddddd;
    --border-radius: 10px;

    /* Shadow */
    --box-shadow: 6px 6px 12px 0 rgba(0, 0, 0, 0.2), -6px -6px 12px 0 rgba(255, 255, 255, 0.5);

    /* Font */
    --font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    --font-weight: 500;
    --letter-spacing: 0.02em;
}

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentcolor);
    }

    body {
        /* Font + Text */
        font-family: var(--font-family), serif;
        font-weight: var(--font-weight);
        letter-spacing: var(--letter-spacing);
        font-size: calc(10pt + 0.3vw);
        text-align: justify;

        /* Margin + Padding */
        margin: 0;
        padding: 0;
        width: 100%;
    }
}

/* HTML: <div class="loader"></div> */
.loader {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 8px solid #d1914b;
    box-sizing: border-box;
    --c: no-repeat radial-gradient(farthest-side, #d64123 94%, #0000);
    --b: no-repeat radial-gradient(farthest-side, #000 94%, #0000);
    background: var(--c) 11px 15px,
    var(--b) 6px 15px,
    var(--c) 35px 23px,
    var(--b) 29px 15px,
    var(--c) 11px 46px,
    var(--b) 11px 34px,
    var(--c) 36px 0px,
    var(--b) 50px 31px,
    var(--c) 47px 43px,
    var(--b) 31px 48px,
    #f6d353;
    background-size: 15px 15px, 6px 6px;
    animation: l4 3s infinite;
}

@keyframes l4 {
    0% {
        -webkit-mask: conic-gradient(#0000 0, #000 0)
    }
    16.67% {
        -webkit-mask: conic-gradient(#0000 60deg, #000 0)
    }
    33.33% {
        -webkit-mask: conic-gradient(#0000 120deg, #000 0)
    }
    50% {
        -webkit-mask: conic-gradient(#0000 180deg, #000 0)
    }
    66.67% {
        -webkit-mask: conic-gradient(#0000 240deg, #000 0)
    }
    83.33% {
        -webkit-mask: conic-gradient(#0000 300deg, #000 0)
    }
    100% {
        -webkit-mask: conic-gradient(#0000 360deg, #000 0)
    }
}
