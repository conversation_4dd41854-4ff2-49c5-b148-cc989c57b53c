translation:
  lang_emoji: 🥺
  app_title: Pizza-Byte UwU
  loading_menu: Woading menu... pwease wait! >w<

  errors:
    failed_to_load_menu: 'Menu woading ewwow: {message} OwO'

  header:
    adminlinks:
      prepare: Pwepawation uwu
      manage_db: Database >w<
      manage_orders: Owdews
      manage_items: Items nya~
      logout: Wogout TwT
    menu:
      login: Wogin
      home: Home uwu
      orders: Owdews

  footer:
    fuel: Code & Pizza by uwu

  order:
    # When owdewing something uwu
    introduction:
      choose_your_item: Choose pwoduct pwease! >w<
      pick_up_time: Pickup time nya~
      pay_in_cash: Cash payment uwu
      closer: Have a gweat evening! UwU
    item:
      button:
        add: Add pwease! owo
    order_summary:
      add: Add pizzas nya~
      open_order_summary: Go to cawt >w<
    order_button:
      order_now: Owdew now! UwU

  order_status:
    order: Owdew
    total: Totaw

    suspense:
      loading: Woading... pwease wait! >w<
    show_at_pickup: Show this page at pickup uwu
    thank_you:
      qr:
        title: Thanks fow youw owdew! 🍕 UwU
        your_order_number: 'Owdew No.:'
        instructions: Pwease pay at the countew! >w<
        order_again: New owdew nya~
        view_orders: My owdews uwu
    status:
      ready_by: Weady by
      ordered: Owdewed
      inPreparation: In pwepawation >w<
      ready: Weady fow pickup! UwU
      delivered: Picked up nya~
      cancelled: Cancewwed TwT
      default: Status unknown :( sowwy!
      paid: Paid yay!
      unpaid: Unpaid >w<
      error: Ewwow TwT
    cancel_order: Cancew owdew

  cart:
    title: Cawt uwu
    messages:
      empty_cart: Cawt empty owo
      add_note: Add note pwease!
      add_more_products: Add mowe items >w<
      name_not_set: Name missing nya~
      timeslot_not_selected: Time swot missing uwu
    timeslot:
      title: Time swot
      subtitle: Choose pickup time pwease! >w<
    order:
      name: Name
      name_placeholder: Youw name uwu
    items: Items nya~
    total_price: Totaw
    currency: €
    select_timeslot: Sewect youw desiwed pickup time pwease! UwU
    open_order_summary: Go to ovewview >w<

  order_overview:
    history:
      title: Histowy
      subtitle: Youw wecent owdews uwu
    recent:
      title: Cuwwent
      orders_found: '{count, plural, =0 {No owdews owo} =1 {One owdew uwu} other {# owdews nya~}} '
      no_orders_yet: No owdews yet >w<
      messages:
        first_order: You'ww see youw owdews hewe! UwU
    timeline:
      title: Timewine
      subtitle: Wive owdews nya~

  admin:
    manage:
      database:
        title: Database >w<
        enable_changes: Enabwe changes uwu
        reset_database: Weset DB nya~
        delete_database: Dewete DB TwT
        system_state: 'System: {state}'

      order:
        errors:
          order_not_found: Owdew not found owo
          barcode_fail: 'Bawcode ewwow: ID not weadabwe >w<'
        order_history: Histowy
        scan_qr_code_text: QW scan fow owdew uwu
        show_finished: Show compweted nya~
        hide_finished: Hide compweted
        status: 'Status:'
        price: 'Pwice:'
        items: 'Items:'
        order_date: 'Date:'
        timeslot: 'Time swot:'
        comment: 'Note:'
        paid: Paid uwu
        not_paid: Outstanding >w<

      pizza:
        errors:
          error_fetching: 'Ewwow: Items not woaded owo'
        title: Items nya~
        subtitle: Cweate & edit items pwease! >w<
        new_pizza_name: New item uwu
        edit_item:
          enabled: Active
          disabled: Inactive
          disable: Disabwe
          enable: Enabwe
          items_available: '{items} avaiwabwe uwu'
          update: Save pwease! >w<
          create: Cweate nya~
          name: Name
          type: Type
          dietary: Detaiws
          price: Pwice
          max_items_available: Max. avaiwabwe

    prepare:
      title: Active Owdews uwu
      current_time: 'Time: {currentTime}'
      open_orders: 'Open: '
      no_open_orders: No open owdews 🎉 yay!
      item: Item
      timeslot: Time swot
      name: Name
      status: Status
      is_done: ✅ Done! UwU

  withsystemcheck:
    check_system_status: Checking system... pwease wait! >w<
    system_inactive: System inactive owo
    system_unavailable_message: System maintenance. Pwease twy again watew! TwT

  components:
    menusection:
      loading: Woading menu... >w<
    timeline:
      loading: Woading timewine... uwu
      warning: 'Wawning: {error}. Showing fawwback data nya~'
    errormessage:
      please_try_again: Twy again pwease! owo
    searchinput:
      placeholder: Seawch... uwu

  login:
    title: Hewpew Wogin UwU
    fsik_token: FSI/K Token >w<
    authenticating: Authenticating... pwease wait! nya~
    login: Wogin uwu
