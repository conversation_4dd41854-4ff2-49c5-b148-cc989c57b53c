{"name": "pizza-app", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Z<PERSON><PERSON><PERSON><PERSON>er"}], "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "build:standalone": "BUILD_STANDALONE=true next build", "start": "next start", "docker:build": "docker-compose -f docker-compose.yml -f docker-compose.db.yml -p pizza build", "docker:up": "docker-compose -f docker-compose.yml -f docker-compose.db.yml -p pizza up", "docker:db:up": "docker-compose -f docker-compose.db.yml -p pizza up", "docker:down": "docker-compose -f docker-compose.yml -f docker-compose.db.yml -p pizza down", "docker:down:volumes": "docker-compose -f docker-compose.yml -f docker-compose.db.yml -p pizza down --volumes"}, "dependencies": {"@date-fns/utc": "^2.1.0", "@tailwindcss/postcss": "^4.1.8", "@tanstack/react-query": "^5.80.7", "@types/js-yaml": "^4.0.9", "@yudiel/react-qr-scanner": "^2.3.1", "accept-language": "^3.0.20", "caniuse-lite": "^1.0.30001718", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "js-yaml": "^4.1.0", "lucide-react": "^0.514.0", "mongoose": "^8.15.1", "next": "latest", "next-intl": "^4.1.0", "qrcode": "^1.5.4", "react": "^19.0.0", "react-dom": "^19.0.0", "recharts": "^2.15.3", "yaml-loader": "^0.8.1", "zustand": "^5.0.5"}, "devDependencies": {"@types/node": "^24.0.0", "@types/react": "^19.1.7", "@types/react-dom": "^19.0.0", "autoprefixer": "^10.4.21", "bun-types": "^1.2.15", "oxlint": "^0.18.1", "postcss": "^8.5.4", "tailwindcss": "^4.1.8", "ts-morph": "^26.0.0", "typescript": "^5.8.3"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}