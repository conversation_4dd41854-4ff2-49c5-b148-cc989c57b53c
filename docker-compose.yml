services:
  mongodb:
    image: mongo:4.2
    env_file:
      - ./.db.env
    # ports:
    #   - '27017:27017'
    volumes: 
      - db_data:/data/db
    networks:
      - db
    restart: unless-stopped

  app:
    container_name: app
    build:
      context: .
      dockerfile: Dockerfile
    env_file:
      - ./.env
    ports:
      - "3000:3000"
    networks:
      - app
    restart: unless-stopped

networks:
  app:
  db:
    driver: bridge
volumes:
  db_data:
