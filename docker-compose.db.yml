services:
  mongodb:
    # container_name: mongo
    image: mongo:4.2
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_DATABASE=auth
      - MONGO_INITDB_ROOT_PASSWORD=pass
    ports:
      - '27017:27017'
    volumes: 
      - db_data:/data/db.deprecated
    networks:
      - db
    restart: unless-stopped
  
  mongodb_manager:
    # container_name: mongo-express
    image: mongo-express
    depends_on:
      - mongodb
    environment:
      - ME_CONFIG_MONGODB_URL=**********************************
      # - ME_CONFIG_MONGODB_SERVER=db.deprecated
      - ME_CONFIG_MONGODB_ADMINUSERNAME=admin
      - ME_CONFIG_MONGODB_ADMINPASSWORD=pass
      - ME_CONFIG_BASICAUTH_USERNAME=admin
      - ME_CONFIG_BASICAUTH_PASSWORD=tribes
    ports:
      - '8081:8081'
    volumes: 
      - db_data:/data/db.deprecated
    networks:
      - db
    restart: unless-stopped


networks:
  app:
  db:
    driver: bridge
volumes:
  db_data:
