// Fill the database
import { ItemModel } from "@/model/item";
import { requireAuth } from "@/lib/auth/serverAuth";
import dbConnect from "@/lib/dbConnect";
import { OrderModel } from "@/model/order";
import { SystemModel } from "@/model/system";
import { EditableConfig } from "@/model/config";
import { DEFAULT_EDITABLE_CONFIG, SYSTEM_NAME } from "@/config";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";


// --- CURRENT APP CONFIGURATION ---
let currentConfig: EditableConfig = { ...DEFAULT_EDITABLE_CONFIG };

/**
 * Loads configuration from MongoDB.
 * If no configuration is found for the SYSTEM_NAME, it creates one with default values.
 */
async function initializeAndLoadConfig(): Promise<EditableConfig> {
    try {
        let systemDoc = await SystemModel.findOne({ name: SYSTEM_NAME });

        if (systemDoc) {
            if (systemDoc.config && Object.keys(systemDoc.config).length > 0) {
                // Merge database config with defaults to ensure all keys are present
                // Database values will override defaults
                currentConfig = {
                    LIFETIME_BEARER_HOURS: systemDoc.config.LIFETIME_BEARER_HOURS ?? DEFAULT_EDITABLE_CONFIG.LIFETIME_BEARER_HOURS,
                    TIME_SLOT_CONFIG: {
                        ...DEFAULT_EDITABLE_CONFIG.TIME_SLOT_CONFIG,
                        ...(systemDoc.config.TIME_SLOT_CONFIG || {}),
                        STATUS_COLORS: {
                            ...DEFAULT_EDITABLE_CONFIG.TIME_SLOT_CONFIG.STATUS_COLORS,
                            ...(systemDoc.config.TIME_SLOT_CONFIG?.STATUS_COLORS || {})
                        },
                        BORDER_STYLES: {
                            ...DEFAULT_EDITABLE_CONFIG.TIME_SLOT_CONFIG.BORDER_STYLES,
                            ...(systemDoc.config.TIME_SLOT_CONFIG?.BORDER_STYLES || {})
                        }
                    },
                    ORDER_CONFIG: {
                        ...DEFAULT_EDITABLE_CONFIG.ORDER_CONFIG,
                        ...(systemDoc.config.ORDER_CONFIG || {}),
                        ORDER_AMOUNT_THRESHOLDS: {
                            ...DEFAULT_EDITABLE_CONFIG.ORDER_CONFIG.ORDER_AMOUNT_THRESHOLDS,
                            ...(systemDoc.config.ORDER_CONFIG?.ORDER_AMOUNT_THRESHOLDS || {})
                        }
                    },
                    ITEM_CONFIG: {
                        ...DEFAULT_EDITABLE_CONFIG.ITEM_CONFIG,
                        ...(systemDoc.config.ITEM_CONFIG || {})
                    }
                };
                console.log(`Configuration for system '${SYSTEM_NAME}' loaded from MongoDB.`);
            } else {
                // System document exists but has no config, so update it with defaults
                systemDoc.config = DEFAULT_EDITABLE_CONFIG;
                await systemDoc.save();
                currentConfig = { ...DEFAULT_EDITABLE_CONFIG };
                console.log(`Configuration for system '${SYSTEM_NAME}' was empty. Initialized with defaults in MongoDB.`);
            }
        } else {
            // No system document found, create a new one with default config
            await SystemModel.create({
                name: SYSTEM_NAME,
                status: { active: true, message: "System initialized with default configuration." },
                config: DEFAULT_EDITABLE_CONFIG,
            });
            currentConfig = { ...DEFAULT_EDITABLE_CONFIG };
            console.log(`No system document found for '${SYSTEM_NAME}'. Created one with default configuration.`);
        }
    } catch (error) {
        console.error(`Error loading or initializing configuration for system '${SYSTEM_NAME}':`, error);
        console.warn(`Using hardcoded default configuration for system '${SYSTEM_NAME}' due to error.`);
        currentConfig = { ...DEFAULT_EDITABLE_CONFIG };
    }
    return currentConfig;
}


const ingredientsByName = {
    // Classic Pizzas
    Margherita: ["Cheese 🧀", "Tomato Sauce 🍅", "Fresh Basil 🌿"],
    Pepperoni: ["Cheese 🧀", "Tomato Sauce 🍅", "Pepperoni 🍕"],
    "Hawaiian": ["Cheese 🧀", "Tomato Sauce 🍅", "Meat 🥓", "Pineapple 🍍"],
    "Meat Lovers": ["Cheese 🧀", "Tomato Sauce 🍅", "Pepperoni 🍕", "Sausage 🌭", "Meat 🥓", "Chicken 🥓"],
    "BBQ Chicken": ["Cheese 🧀", "BBQ Sauce 🍖", "Grilled Chicken 🐔", "Red Onions 🧅", "Cilantro 🌿"],

    // Gourmet Pizzas
    "Four Cheese": ["Mozzarella 🧀", "Parmesan 🧀", "Gorgonzola 🧀", "Ricotta 🧀", "White Sauce 🥛"],
    "Mediterranean": ["Cheese 🧀", "Tomato Sauce 🍅", "Olives 🫒", "Sun-dried Tomatoes 🍅", "Feta 🧀", "Basil 🌿"],
    "Truffle Mushroom": ["Cheese 🧀", "White Sauce 🥛", "Mixed Mushrooms 🍄", "Truffle Oil 🫒", "Parmesan 🧀"],

    // Vegetarian Pizzas
    "Veggie Supreme": ["Cheese 🧀", "Tomato Sauce 🍅", "Mushrooms 🍄", "Bell Peppers 🫑", "Red Onions 🧅", "Olives 🫒"],
    "Spinach Ricotta": ["Cheese 🧀", "White Sauce 🥛", "Fresh Spinach 🥬", "Ricotta 🧀", "Garlic 🧄"],

    // Vegan Pizzas
    "Vegan Margherita": ["Vegan Cheese 🧀", "Tomato Sauce 🍅", "Fresh Basil 🌿"],
    "Vegan Garden": ["Vegan Cheese 🧀", "Tomato Sauce 🍅", "Spinach 🥬", "Mushrooms 🍄", "Bell Peppers 🫑", "Red Onions 🧅"],
    "Vegan Hawaiian": ["Vegan Cheese 🧀", "Tomato Sauce 🍅", "Vegan Meat 🥓", "Pineapple 🍍"],

    // Sides
    "Garlic Bread": ["Fresh Bread 🍞", "Garlic Butter 🧄", "Herbs 🌿"],
    "Cheesy Garlic Bread": ["Fresh Bread 🍞", "Garlic Butter 🧄", "Mozzarella 🧀", "Herbs 🌿"],
    "Buffalo Wings": ["Chicken Wings 🐔", "Buffalo Sauce 🌶️", "Celery 🥬"],
    "Caesar Salad": ["Romaine Lettuce 🥬", "Parmesan 🧀", "Croutons 🍞", "Caesar Dressing 🥗"],
    "Greek Salad": ["Mixed Greens 🥬", "Feta 🧀", "Olives 🫒", "Tomatoes 🍅", "Cucumber 🥒", "Greek Dressing 🥗"],

    // Desserts
    "Tiramisu": ["Mascarpone 🧀", "Coffee ☕", "Ladyfingers 🍪", "Cocoa 🍫"],
    "Chocolate Brownie": ["Dark Chocolate 🍫", "Butter 🧈", "Vanilla Ice Cream 🍦"],
    "Gelato": ["Italian Gelato 🍦"]
};

export async function GET() {
    // Initialize system if it does not exist
    await dbConnect();
    await initializeAndLoadConfig()
    return Response.json({ status: "success" });
}

/**
 * Delete the database
 * @constructor
 */
export async function POST() {
    await dbConnect();
    await requireAuth();

    await ItemModel.deleteMany({})
    await OrderModel.deleteMany({})
    await SystemModel.deleteMany({})

    // Add complete menu
    const menuItems = [
        // === CLASSIC PIZZAS ===
        {
            name: 'Margherita',
            price: 8,
            type: 'pizza',
            dietary: 'vegetarian',
            ingredients: ingredientsByName['Margherita'],
            size: 1,
            enabled: true
        },
        {
            name: 'Pepperoni',
            price: 10,
            type: 'pizza',
            dietary: 'meat',
            ingredients: ingredientsByName['Pepperoni'],
            size: 1,
            enabled: true
        },
        {
            name: 'Hawaiian',
            price: 11,
            type: 'pizza',
            dietary: 'meat',
            ingredients: ingredientsByName['Hawaiian'],
            size: 1,
            enabled: true
        },
        {
            name: 'Meat Lovers',
            price: 14,
            type: 'pizza',
            dietary: 'meat',
            ingredients: ingredientsByName['Meat Lovers'],
            size: 1,
            enabled: true
        },
        {
            name: 'BBQ Chicken',
            price: 12,
            type: 'pizza',
            dietary: 'meat',
            ingredients: ingredientsByName['BBQ Chicken'],
            size: 1,
            enabled: true
        },

        // === GOURMET PIZZAS ===
        {
            name: 'Four Cheese',
            price: 13,
            type: 'pizza',
            dietary: 'vegetarian',
            ingredients: ingredientsByName['Four Cheese'],
            size: 1,
            enabled: true
        },
        {
            name: 'Mediterranean',
            price: 12,
            type: 'pizza',
            dietary: 'vegetarian',
            ingredients: ingredientsByName['Mediterranean'],
            size: 1,
            enabled: true
        },
        {
            name: 'Truffle Mushroom',
            price: 15,
            type: 'pizza',
            dietary: 'vegetarian',
            ingredients: ingredientsByName['Truffle Mushroom'],
            size: 1,
            enabled: true
        },

        // === VEGETARIAN PIZZAS ===
        {
            name: 'Veggie Supreme',
            price: 10,
            type: 'pizza',
            dietary: 'vegetarian',
            ingredients: ingredientsByName['Veggie Supreme'],
            size: 1,
            enabled: true
        },
        {
            name: 'Spinach Ricotta',
            price: 11,
            type: 'pizza',
            dietary: 'vegetarian',
            ingredients: ingredientsByName['Spinach Ricotta'],
            size: 1,
            enabled: true
        },

        // === VEGAN PIZZAS ===
        {
            name: 'Vegan Margherita',
            price: 9,
            type: 'pizza',
            dietary: 'vegan',
            ingredients: ingredientsByName['Vegan Margherita'],
            size: 1,
            enabled: true
        },
        {
            name: 'Vegan Garden',
            price: 11,
            type: 'pizza',
            dietary: 'vegan',
            ingredients: ingredientsByName['Vegan Garden'],
            size: 1,
            enabled: true
        },
        {
            name: 'Vegan Hawaiian',
            price: 12,
            type: 'pizza',
            dietary: 'vegan',
            ingredients: ingredientsByName['Vegan Hawaiian'],
            size: 1,
            enabled: true
        },

        // === SIDES ===
        {
            name: 'Garlic Bread',
            price: 4,
            type: 'side',
            dietary: 'vegetarian',
            ingredients: ingredientsByName['Garlic Bread'],
            size: 1,
            enabled: true
        },
        {
            name: 'Cheesy Garlic Bread',
            price: 6,
            type: 'side',
            dietary: 'vegetarian',
            ingredients: ingredientsByName['Cheesy Garlic Bread'],
            size: 1,
            enabled: true
        },
        {
            name: 'Buffalo Wings',
            price: 8,
            type: 'side',
            dietary: 'meat',
            ingredients: ingredientsByName['Buffalo Wings'],
            size: 1,
            enabled: true
        },
        {
            name: 'Caesar Salad',
            price: 7,
            type: 'salad',
            dietary: 'vegetarian',
            ingredients: ingredientsByName['Caesar Salad'],
            size: 1,
            enabled: true
        },
        {
            name: 'Greek Salad',
            price: 8,
            type: 'salad',
            dietary: 'vegetarian',
            ingredients: ingredientsByName['Greek Salad'],
            size: 1,
            enabled: true
        },

        // === DRINKS ===
        {
            name: 'Coca Cola',
            price: 3,
            type: 'drink',
            dietary: 'vegan',
            ingredients: ['Cola 🥤'],
            size: 1,
            enabled: true
        },
        {
            name: 'Sprite',
            price: 3,
            type: 'drink',
            dietary: 'vegan',
            ingredients: ['Lemon-Lime Soda 🥤'],
            size: 1,
            enabled: true
        },
        {
            name: 'Orange Juice',
            price: 4,
            type: 'drink',
            dietary: 'vegan',
            ingredients: ['Fresh Orange Juice 🍊'],
            size: 1,
            enabled: true
        },
        {
            name: 'Sparkling Water',
            price: 2,
            type: 'drink',
            dietary: 'vegan',
            ingredients: ['Sparkling Water 💧'],
            size: 1,
            enabled: true
        },
        {
            name: 'Italian Beer',
            price: 5,
            type: 'drink',
            dietary: 'vegan',
            ingredients: ['Premium Italian Beer 🍺'],
            size: 1,
            enabled: true
        },

        // === DESSERTS ===
        {
            name: 'Tiramisu',
            price: 6,
            type: 'dessert',
            dietary: 'vegetarian',
            ingredients: ingredientsByName['Tiramisu'],
            size: 1,
            enabled: true
        },
        {
            name: 'Chocolate Brownie',
            price: 5,
            type: 'dessert',
            dietary: 'vegetarian',
            ingredients: ingredientsByName['Chocolate Brownie'],
            size: 1,
            enabled: true
        },
        {
            name: 'Vanilla Gelato',
            price: 4,
            type: 'dessert',
            dietary: 'vegetarian',
            ingredients: ingredientsByName['Gelato'],
            size: 1,
            enabled: true
        },
        {
            name: 'Chocolate Gelato',
            price: 4,
            type: 'dessert',
            dietary: 'vegetarian',
            ingredients: ingredientsByName['Gelato'],
            size: 1,
            enabled: true
        },
        {
            name: 'Strawberry Gelato',
            price: 4,
            type: 'dessert',
            dietary: 'vegetarian',
            ingredients: ingredientsByName['Gelato'],
            size: 1,
            enabled: true
        }
    ];

    // Save all menu items
    for (const item of menuItems) {
        await new ItemModel(item).save();
    }

    await initializeAndLoadConfig()

    return Response.json({ message: 'Successfully reset system' })
}
