name: CI/CD
on:
  push:
    branches:
      - main
jobs:
  main:
    name: Update Pizza repository and build Docker image on trinity after push to master
    runs-on: ubuntu-latest
    steps:
    - name: Execute remote ssh commands using ssh key
      uses: appleboy/ssh-action@v1.2.0
      with:
        host: trinity.fsi.uni-tuebingen.de
        username: ${{ secrets.SSH_USERNAME }}
        key: ${{ secrets.SSH_KEY }}
        port: ${{ secrets.SSH_PORT }}
        # stored in secrets because it's easier to change this instead of updating this script
        # i.e. when switching from RSA host key to ED25519
        fingerprint: ${{ secrets.SSH_FINGERPRINT }}
        script: |
          sudo -u root /bin/bash /srv/docker/pizzasystem/update.sh
